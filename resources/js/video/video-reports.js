(function () {
    'use strict';

    angular.module('thankview.video')
        .directive('tvVideoReports', ['$rootScope', '$timeout', 'ajax', '$window', '$document', '$q', function ($rootScope, $timeout, ajax, $window, $document, $q) {
            return {
                restrict: 'A',
                scope: true,
                link: function (scope, element) {
                    let dontTrack = false;
                    let reportDomain = '';
                    let isPreview;
                    let isLanding;
                    let reportContainerEl;

                    scope.renderPDF = (report) => {
                        if (!scope.hasAccess) {
                            return;
                        }

                        // only get PDF if first time on selected report tab
                        if (!report.fetched) {
                            // empty the canvas container so previewing
                            // several reports doesn't stack reports on top
                            // of each other
                            const theContainer = $document[0].getElementById('the-container-' + report.order);
                            theContainer.innerHTML = '';
                            report.fetched = true;

                            const urlPath = report.path + (report.params ? '?' + report.params : '');

                            if (!config.isMobile && !config.isiPad) {
                                scope.renderPDFIFrame(report, urlPath);
                            } else {
                                scope.renderPDFJs(report, urlPath);
                            }
                        }
                    };

                    scope.renderPDFIFrame = (report, urlPath) => {
                        // wait until the container is rendered
                        // to calculate it's width
                        const theContainer = $document[0].getElementById('the-container-' + report.order);
                        if (theContainer.clientWidth === 0) {
                            $timeout(() => {
                                scope.renderPDFIFrame(report, urlPath);
                            }, 100);
                            return;
                        }

                        const iframe = $document[0].createElement('iframe');
                        iframe.src = urlPath + '#toolbar=0&navpanes=0';
                        iframe.width = '100%';
                        const calculatedHeight = Math.min(report.height * theContainer.clientWidth / report.width * 0.9, 3000);
                        iframe.height = calculatedHeight + 'px';

                        theContainer.appendChild(iframe);
                    };

                    scope.renderPDFJs = (report, urlPath) => {
                        const loadingTask = $window.pdfjsLib.getDocument(urlPath);

                        loadingTask.promise.then((pdf) => {
                            report.currPage = 1;
                            report.pdfFile = pdf;
                            report.numPages = pdf.numPages;

                            pdf.getPage(1).then((page) => {
                                scope.handlePages(page, report);
                            });
                        });
                    };

                    scope.handlePages = (page, report) => {
                        const theContainer = $document[0].getElementById('the-container-' + report.order);
                        const scale = 2;
                        const viewport = page.getViewport({ scale: scale });

                        const canvas = $document[0].createElement('canvas');
                        theContainer.appendChild(canvas);

                        const context = canvas.getContext('2d');
                        canvas.height = viewport.height;
                        canvas.width = viewport.width;
                        canvas.classList.add('width-100', 'pad-t-8px', 'pad-r-8px', 'pad-l-8px', 'lg-pad-t-16px', 'lg-pad-r-16px', 'lg-pad-l-16px');

                        const renderContext = {
                            canvasContext: context,
                            viewport: viewport,
                        };

                        page.render(renderContext);
                        report.currPage += 1;

                        if (report.pdfFile !== null && report.currPage <= report.numPages) {
                            report.pdfFile.getPage(report.currPage).then((pdfPage) => {
                                scope.handlePages(pdfPage, report);
                            });
                        }
                    };

                    scope.init = function () {
                        reportContainerEl = element[0].querySelector('.video-report');

                        scope.reportWidth = reportContainerEl.clientWidth;
                        scope.reports = config.params.reports;

                        scope.videoId = element.attr('data-id');
                        scope.guestId = null;
                        scope.hasAccess = config.params.project.no_odder_password;

                        // check if thankview shouldn't be tracked
                        const pathNames = $window.location.pathname.split('/');
                        isPreview = pathNames[2] && pathNames[2] === 'preview';
                        isLanding = pathNames[2] && pathNames[2] === 'landing';
                        dontTrack = !!(isLanding || isPreview || ($window.location.search && $window.location.search.indexOf('source=export') >= 0));

                        scope.setReportDomain();

                        angular.element($window).on('scroll', scope.calcReportScroll);

                        // allow parent to preview odder report
                        $window.previewOdderReport = scope.previewPersonalReport;

                        $timeout(() => {
                            scope.changeTabs(scope.reports[0]);
                        }, 20);
                    };

                    scope.setReportDomain = function () {
                        angular.forEach(config.params.report_countries, function (country) {
                            if (country.name === config.params.project.odder_country) {
                                reportDomain = country.url;
                            }
                        });
                    };

                    scope.calcReportScroll = function () {
                        if (!scope.hasAccess || !scope.currentReport) {
                            return;
                        }

                        if (scope.currentReport.height) {
                            const windowHeight = $window.innerHeight;
                            const reportTop = reportContainerEl.getBoundingClientRect().top;
                            const reportPageHeight = scope.currentReport.height * reportContainerEl.clientWidth / (scope.currentReport.width * scope.currentReport.num_pages);

                            let pagesViewed = Math.ceil((windowHeight - reportTop) / reportPageHeight);
                            pagesViewed = Math.min(pagesViewed, scope.currentReport.num_pages);

                            // default pages viewed to 1
                            if (!scope.currentReport.pagesViewed) {
                                scope.currentReport.pagesViewed = 1;
                            }

                            if (pagesViewed > scope.currentReport.pagesViewed) {
                                scope.currentReport.pagesViewed = pagesViewed;

                                scope.trackEvent('num_page_views', pagesViewed);
                            }
                        }
                    };

                    scope.changeTabs = function (report) {
                        scope.currentReport = report;
                        scope.reportGuestId = null;

                        // if odder password isn't used, fetch the personal report
                        if (scope.hasAccess && report.is_personal) {
                            if (isLanding) {
                                report.path = null;
                            }
                            scope.fetchPersonalReport(report);
                        }

                        if (scope.hasAccess) {
                            scope.renderPDF(report);
                            scope.trackView(report);
                        }
                    };

                    scope.previewPersonalReport = function (guest, password) {
                        if (!isLanding) {
                            return;
                        }

                        angular.forEach(scope.reports, (report) => {
                            if (report.is_personal) {
                                report.fetched = false;
                            }
                        });

                        scope.hasAccess = true;
                        scope.password = password;
                        scope.guestId = guest.id;
                        scope.changeTabs(scope.currentReport);
                    };

                    scope.checkPassword = function (report, password) {
                        if (scope.hasAccess) {
                            return;
                        }

                        if (password) {
                            scope.password = password;
                        }

                        const params = {
                            password: scope.password,
                            videoId: scope.videoId,
                        };

                        $rootScope.$broadcast('open-loading');

                        ajax.post('/api/reportGuest/password', params).then(function () {
                            $rootScope.$broadcast('close-loading');

                            scope.hasAccess = true;

                            if (report.is_personal) {
                                scope.fetchPersonalReport(report);
                            }

                            scope.renderPDF(report);
                            scope.trackView(report);
                        });
                    };

                    scope.fetchPersonalReport = function (report) {
                        report.path = reportDomain + '/api/reportGuest/personal/' + slugify(report.name);
                        report.params = 'password=' + scope.password;
                        if (scope.videoId) {
                            report.params += '&videoId=' + scope.videoId;
                        }
                        if (scope.guestId) {
                            report.params += '&guestId=' + scope.guestId;
                        }
                        report.params += '&reportId=' + report.id;
                        report.params += '&region=' + config.db;
                    };

                    scope.downloadReport = function (report) {
                        // slugifies name
                        if (!report.is_personal) {
                            const filename = report.name.replace(/[^a-z0-9]/gi, '_').toLowerCase() + '.pdf';
                            const link = $document[0].createElement('a');
                            link.target = '_blank';
                            link.download = filename;
                            link.href = report.download_path;
                            link.click();
                        } else {
                            $window.open(report.path + '?download=1&' + report.params, '_blank');
                        }

                        scope.trackEvent('download');
                    };

                    scope.shareReport = function () {
                        let mailToLink = 'mailto:';
                        mailToLink += '?subject=';
                        const reportType = config.params.project.project_type.slug === 'odder' ? 'Endowment Report' : 'PDF attachment';
                        if (!config.params.project.share_title) {
                            mailToLink += encodeURIComponent(config.params.reply_from_name + ' wants to share their ' + reportType);
                        } else {
                            mailToLink += encodeURIComponent(config.params.project.share_title);
                        }
                        if (!config.params.project.share_message) {
                            mailToLink += '&body=' + encodeURIComponent(config.params.business.full_name + ' prepared a digital version of my ' + reportType + '. Access it by clicking the link below:');
                            if (scope.password) {
                                mailToLink += encodeURIComponent(' and entering in the password ' + scope.password);
                            }
                        } else {
                            mailToLink += '&body=' + encodeURIComponent(config.params.project.share_message);
                        }
                        mailToLink += '%0D%0A%0D%0A' + $window.location.href;

                        $window.open(mailToLink, '_blank');

                        scope.trackEvent('share');
                    };

                    scope.printReport = function (report) {
                        if (!report.is_personal) {
                            $window.open(report.path, '_blank');
                        } else {
                            $window.open(report.path + '?' + report.params, '_blank');
                        }

                        scope.trackEvent('print');
                    };

                    scope.orderReport = function () {
                        var deferred = $q.defer();

                        $rootScope.$broadcast('open-message-confirm', 'Are you sure you want to request a printed copy?', deferred);

                        deferred.promise.then(function () {
                            scope.trackEvent('requested_print');

                            const message = 'Your request has been sent!';
                            $rootScope.$broadcast('open-message', message);
                        });
                    };

                    scope.trackView = function (report) {
                        if (!scope.videoId || dontTrack) return;

                        const params = {
                            reportId: report.id,
                            videoId: scope.videoId,
                        };

                        ajax.get('/api/reportGuest/view', params).then(function (data) {
                            scope.reportGuestId = data.id;
                        });
                    };

                    scope.trackEvent = function (name, value) {
                        if (!scope.videoId || dontTrack) return;

                        if (!scope.reportGuestId) {
                            $timeout(function () {
                                scope.trackEvent(name, value);
                            }, 250);
                            return;
                        }
                        const params = {
                            id: scope.reportGuestId,
                            name: name,
                            value: value,
                        };

                        ajax.get('/api/reportGuest/event', params);
                    };

                    scope.init();
                },
            };
        }]);
}());
