plugin: amazon.aws.aws_ec2

regions:
  - us-east-1
  - us-east-2
  - us-west-1
  - ca-central-1
filters:
  instance-state-name: running
  #"tag:Environment": prod
  "tag:Environment": prod-test
keyed_groups:
  - key: placement.availability_zone
    prefix: az
    separator: ''
  - key: tags.Name
    prefix: ''
    separator: ''
compose:
  ansible_host: public_ip_address
  ansible_user: "{{ 'ubuntu' if 'ubuntu' in tags.OS|default('') else 'ec2-user' if 'amazon' in tags.OS|default('') else 'ssm-user' if 'tv-stage-webserver' in tags.Name|default('') else 'ubuntu' }}"
groups:
  webservers: "tags.Name is defined and 'webserver-prod' in tags.Name"
  video_workers: "tags.Name is defined and 'video-worker-prod' in tags.Name"
  send_servers: "tags.Name is defined and 'sends-server' in tags.Name"
  prod_media: "tags.Name is defined and 'media' in tags.Name"
  prod_others: "tags.Name is defined and ('scheduler' in tags.Name or 'sftp' in tags.Name or 'monitor' in tags.Name)"
  prod_secure: "tags.Name is defined and 'secure' in tags.Name"
  builder: "tags.Name is defined and 'prod-builder' in tags.Name"
  redis: "tags.Name is defined and 'redis' in tags.Name"
  prod_api_host: "tags.Name is defined and 'api' in tags.Name"
  prod_aws_workers: "tags.Name is defined and ('video-worker-prod' in tags.Name or 'sends-server' in tags.Name)"
  #prod_ca: "tags.Name is defined and 'ca' in tags.Name"





# plugin: amazon.aws.aws_ec2
# regions:
#   - us-east-1
#   - us-east-2
#   - us-west-1
#   - ca-central-1
# filters:
#   instance-state-name: running
#   "tag:Environment": prod
# hostnames:
#   - private-ip-address
# keyed_groups:
#   - key: placement.availability_zone
#     prefix: az
#     separator: ""
#   - key: tags.Name
#     prefix: ""
#     separator: ""
# compose:
#   # Connect over private IP
#   ansible_host: "{{ private_ip_address }}"
#   # Pick ssh user
#   ansible_user: >-
#     {{ 'ubuntu' if 'ubuntu' in tags.OS|default('') else
#        'ec2-user' if 'amazon' in tags.OS|default('') else
#        'ssm-user' if 'tv-stage-webserver' in tags.Name|default('') else
#        'ubuntu' }}
# groups:
#   webservers:    "tags.Name is defined and 'webserver-prod' in tags.Name"
#   video_workers: "tags.Name is defined and 'video-worker-prod' in tags.Name"
#   send_servers:  "tags.Name is defined and 'sends-server' in tags.Name"
#   prod_media:    "tags.Name is defined and 'media' in tags.Name"
#   prod_others:   "tags.Name is defined and ('scheduler' in tags.Name or 'sftp' in tags.Name)"
#   prod_secure:   "tags.Name is defined and 'secure' in tags.Name"
#   builder:       "tags.Name is defined and 'prod-builder' in tags.Name"
#   redis:         "tags.Name is defined and 'redis' in tags.Name"
#   prod_api_host: "tags.Name is defined and 'api' in tags.Name"

