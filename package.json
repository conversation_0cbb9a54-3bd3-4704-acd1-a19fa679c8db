{"devDependencies": {"@babel/preset-react": "^7.16.7", "@bugsnag/source-maps": "^2.3.1", "@commitlint/cli": "^12.1.1", "@commitlint/config-conventional": "^12.1.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.0.0", "@vue/test-utils": "^1.2.2", "angular-mocks": "1.5.11", "angularjs-jest": "^0.1.4", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "bread-compressor-cli": "^1.1.0", "browser-sync-webpack-plugin": "^2.0.1", "cross-env": "^7.0", "eslint": "^8.5.0", "eslint-config-airbnb": "^19.0.2", "eslint-plugin-angular": "^4.1.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.27.1", "husky": "^4.3.0", "jest": "^25.1.0", "jest-cli": "^25.1.0", "kss": "^3.0.1", "laravel-mix": "^6.0.39", "laravel-mix-polyfill": "^3.0.1", "laravel-mix-purgecss": "^6.0.0", "minimist": "^1.2.5", "resolve-url-loader": "^3.1.0", "sass": "^1.34.0", "sass-loader": "^8.0.0", "stylelint": "^14.2.0", "stylelint-config-sass-guidelines": "^9.0.1", "vue-jest": "^3.0.7", "vue-loader": "^15.9.8", "vue-template-compiler": "^2.6.14"}, "dependencies": {"@evertrue/tv-components": "^0.2.1", "@floating-ui/react-dom": "^0.6.3", "@sentry/browser": "^6.19.7", "@sentry/cli": "^2.40.0", "@sentry/integrations": "^6.19.7", "@tensorflow-models/body-pix": "^2.2.1", "@tensorflow/tfjs-backend-webgl": "^4.12.0", "a11y-dialog": "^7.1.0", "angular": "^1.8.2", "angular-legacy-sortablejs-maintained": "^0.6.2", "angular-touch": "^1.8.2", "axios": "^1.5.0", "chart.js": "^3.7.0", "core-js": "^3.21.0", "dayjs": "^1.10.4", "fix-webm-duration": "^1.0.0", "focus-visible": "^5.2.0", "html2canvas": "^1.0.0-rc.7", "jest-junit": "^9.0.0", "launchdarkly-js-client-sdk": "^3.5.0", "lodash": "^4.17.21", "ngVue": "^1.7.8", "prop-types": "^15.8.1", "react": "^18.0.0", "react-dom": "^18.0.0", "react-draggable": "^4.4.6", "react-icons": "^4.9.0", "react-media-recorder-2": "^1.6.23", "react-spinners": "^0.13.8", "react-tooltip": "^5.26.3", "react-transition-group": "^4.4.2", "recordrtc": "^5.6.2", "reselect": "^4.1.8", "sortablejs": "^1.10.2", "stripe": "^8.176.0", "url-search-params-polyfill": "^8.1.1", "video.js": "^8.9.0", "videojs-hotkeys": "^0.2.28", "vue": "^2.6.14"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "resolutions": {"natives": "1.1.3"}, "scripts": {"css-doc": "npm run dev && mkdir -p public/styleguide/css && cp ./public/build/css/global.css ./public/styleguide/css && cp ./public/build/css/account.css ./public/styleguide/css && cd ./tv-styleguide && npm run sass && node ../node_modules/.bin/kss --config ../kss-config.json", "dev": "npm run development", "development": "mix", "test": "jest --verbose", "test-watch": "jest --verbose --watch", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production && npm run compress && npm run sentry:sourcemaps", "production": "mix --production", "compress": "bread-compressor -s public/build/css/*.css public/build/js/*.js", "prestaging": "npm run production && npm run compress", "staging": "rsync -av -e 'ssh -p 8888 -i ~/.ssh/thankview-qa-ec2.pem -o \"StrictHostKeyChecking no\"' public/build ssm-user@localhost:/var/www/thank-views/public", "poststaging": "php vendor/bin/envoy run deploy-staging", "deploy": "npm run deploy-web1 && npm run deploy-web2 && npm run deploy-web3 && npm run deploy-web4 && npm run deploy-media && npm run deploy-sourcemaps", "deploy-web1": "rsync -av public/build ubuntu@*************:/var/www/thank-views/public", "deploy-web2": "rsync -av public/build ubuntu@***********:/var/www/thank-views/public", "deploy-web3": "rsync -av public/build ubuntu@**************:/var/www/thank-views/public", "deploy-web4": "rsync -av public/build ubuntu@************:/var/www/thank-views/public", "deploy-media": "rsync -av public/build ubuntu@************:/var/www/thank-views/public", "deploy-sourcemaps": "bugsnag-source-maps upload-browser --api-key 461ddd737cd53a7d05f60aa8d72791d0 --detect-app-version --base-url https://*.thankview.com/build/js/ --directory public/build/js --overwrite", "lint": "npx eslint . --ext .jsx,.js", "lint-fix": "npx eslint . --ext .jsx,.js --fix", "lint-css": "npx stylelint '**/*.{css,scss}'", "lint-css-fix": "npx stylelint '**/*.{css,scss}' --fix", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org evertrue --project thankview-frontend ./public/build && sentry-cli sourcemaps upload --org evertrue --project thankview-frontend ./public/build"}, "jest": {"globals": {"config": {}}, "transform": {".*\\.(vue)$": "vue-jest"}}, "jest-junit": {"suiteName": "jest tests", "outputDirectory": ".", "outputName": "jest-report.xml", "uniqueOutputName": "false", "ancestorSeparator": " › ", "usePathForSuiteName": "true"}, "version": "1.40.26"}