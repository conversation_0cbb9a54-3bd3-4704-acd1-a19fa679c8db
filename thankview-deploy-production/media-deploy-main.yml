---
# media-deploy-main.yml - Equivalent of the Envoy deploy-prod-media task

- name: Deploy to media servers
  hosts: prod_media
  vars:
    app_path: /var/www/thank-views
    git_repo: "**************:evertrue/ThankView-App.git"  # Updated with correct repo
    git_branch: "{{ deploy_branch | default('production') }}"
    user: "www-data"

  tasks:
    - name: Ensure .ssh directory exists
      ansible.builtin.file:
        path: "/home/<USER>/.ssh"
        state: directory
        mode: '0700'
        owner: "{{ user }}"
        group: "{{ user }}"
      become: true

    - name: Get SSH key using AWS CLI
      ansible.builtin.shell: |
        aws ssm get-parameter --name "/thankview-prod/thankview_deploy" --with-decryption --region us-east-1 --query Parameter.Value --output text
      register: ssh_key_cli
      delegate_to: localhost
      no_log: true

    - name: Save SSH key from CLI to file
      ansible.builtin.copy:
        content: "{{ ssh_key_cli.stdout }}\n"
        dest: "/home/<USER>/.ssh/thankview_deploy"
        mode: '0600'
        owner: "{{ user }}"
        group: "{{ user }}"
      become: true
      no_log: true

    - name: Update apt cache accepting repository changes
      ansible.builtin.shell:
        cmd: apt update --allow-releaseinfo-change
      environment:
        DEBIAN_FRONTEND: noninteractive
      become: true
      ignore_errors: yes

    - name: Fix MySQL repository GPG key
      apt_key:
        keyserver: keyserver.ubuntu.com
        id: B7B3B788A8D3785C
        state: present
      become: yes

    - name: Install Python pip
      apt:
        name: python3-pip
        state: present
        update_cache: yes
      become: true

    - name: Install required Python modules for AWS operations
      pip:
        name:
          - boto3
          - botocore
        state: present
        executable: pip3
        extra_args: "--system"  # Install system-wide
      become: true

    - name: Check if git repo already exists
      ansible.builtin.stat:
        path: "{{ app_path }}/.git"
      register: git_dir

    - name: Ensure proper ownership of the Git repository
      ansible.builtin.file:
        path: "{{ app_path }}"
        state: directory
        recurse: yes
        owner: "{{ user }}"
        group: "{{ user }}"
      become: true

    - name: Clone git repository if it doesn't exist
      ansible.builtin.git:
        repo: "{{ git_repo }}"
        dest: "{{ app_path }}"
        key_file: "/home/<USER>/.ssh/thankview_deploy"
        accept_hostkey: yes
        ssh_opts: "-o StrictHostKeyChecking=no"
      when: not git_dir.stat.exists
      become: true
      become_user: "{{ user }}"

#This will reset repo and drop any local changes before pulling latest repo commit

    - name: Allow Git to work in this repository
      ansible.builtin.shell:
        cmd: git config --global --add safe.directory /var/www/thank-views
      become: true
      become_user: "{{ user }}"

    - name: Reset repository to the latest commit
      ansible.builtin.shell:
        cmd: |
          git reset --hard HEAD
          git clean -fd
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: "{{ user }}"

    - name: Pull latest code if repo exists
      ansible.builtin.git:
        repo: "{{ git_repo }}"
        dest: "{{ app_path }}"
        update: yes
        version: "{{ git_branch }}"
        key_file: "/home/<USER>/.ssh/thankview_deploy"
        accept_hostkey: yes
        ssh_opts: "-o StrictHostKeyChecking=no"
        force: yes  # Add this line to force the checkout not in stage playbook
      when: git_dir.stat.exists
      become: true
      become_user: "{{ user }}"

    - name: Run composer update
      community.general.composer:
        command: update
        working_dir: "{{ app_path }}"
        prefer_dist: true
        no_dev: false
      environment:
        COMPOSER_DISCARD_CHANGES: "true"
      become: true
      become_user: "{{ user }}"
      register: composer_result
      failed_when:
        - composer_result.failed == true
        - "'ddtrace.so' not in composer_result.msg"
        - "'Script php artisan package:discover handling the post-autoload-dump event returned with error code 1' not in composer_result.msg"

    - name: Ensure all dependencies are installed
      ansible.builtin.shell:
        cmd: composer install --prefer-dist --optimize-autoloader
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: "{{ user }}"

    - name: Save git commit hash
      ansible.builtin.shell: git log -1 --pretty=%h > git_commit
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: "{{ user }}"

    - name: Create .profile file for www-data if it doesn't exist
      ansible.builtin.file:
        path: "/home/<USER>/.profile"
        state: touch
        owner: "{{ user }}"
        group: "{{ user }}"
        mode: '0644'
      become: true

    - name: Clear bootstrap cache to remove stale service providers
      ansible.builtin.file:
        path: "{{ item }}"
        state: absent
      loop:
        - "{{ app_path }}/bootstrap/cache/services.php"
        - "{{ app_path }}/bootstrap/cache/packages.php"
      become: true
      become_user: "{{ user }}"

    - name: Regenerate autoloader after clearing cache
      ansible.builtin.shell:
        cmd: composer dump-autoload
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: "{{ user }}"

    - name: Clear Laravel caches
      ansible.builtin.shell: "php artisan {{ item }}"
      args:
        chdir: "{{ app_path }}"
      loop:
        - "config:clear"
        - "route:clear"
        - "view:clear"
      become: true
      become_user: "{{ user }}"

    - name: Restart queue workers
      ansible.builtin.shell: php artisan queue:restart
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: "{{ user }}"

    # New task to fetch and update .env files from AWS Secrets Manager
    - name: Overwrite .env files from Secrets Manager via lookup
      become: true
      block:
        - name: Gather EC2 metadata facts
          ec2_metadata_facts:
          register: ec2_metadata

        - name: Get instance ID from metadata
          set_fact:
            instance_id: "{{ ec2_metadata.ansible_facts.ansible_ec2_instance_id }}"

        - name: Get EC2 instance info to determine server name
          ec2_instance_info:
            instance_ids: "{{ instance_id }}"
            region: "{{ ec2_metadata.ansible_facts.ansible_ec2_placement_region }}"
          register: server_info
          delegate_to: localhost
          become: false

        - name: Extract server name from EC2 Name tag
          set_fact:
            server_name: "{{ item.value }}"
          loop: "{{ server_info.instances[0].tags | dict2items }}"
          when: item.key == 'Name'

        - name: Debug server name
          debug:
            msg: "Server name: {{ server_name }}"

        - name: Define potential app paths to check
          set_fact:
            potential_app_paths:
              - path: /var/www/thank-views
                secret_suffix: "-env"
              - path: /var/www/thank-views-ca
                secret_suffix: "-ca-env"

        - name: Check which app paths exist on this server
          stat:
            path: "{{ item.path }}"
          loop: "{{ potential_app_paths }}"
          register: app_path_stats
          loop_control:
            label: "{{ item.path }}"

        - name: Build list of existing app paths with their secrets
          set_fact:
            existing_app_paths: "{{ existing_app_paths | default([]) + [{'path': item.item.path, 'secret_name': 'prod/' + server_name + item.item.secret_suffix}] }}"
          loop: "{{ app_path_stats.results }}"
          when: item.stat.exists and item.stat.isdir
          loop_control:
            label: "{{ item.item.path }}"

        - name: Debug existing app paths and their secrets
          debug:
            msg: "Server {{ server_name }} has app path {{ item.path }} using secret {{ item.secret_name }}"
          loop: "{{ existing_app_paths }}"

        - name: "Fetch secrets for each app path"
          set_fact:
            "{{ item.path | regex_replace('/', '_') | regex_replace('-', '_') }}_json": "{{ lookup('aws_secret', item.secret_name, region='us-east-1') }}"
          loop: "{{ existing_app_paths }}"
          loop_control:
            label: "{{ item.secret_name }}"

        - name: "Convert JSON to .env format for each app path"
          set_fact:
            "{{ item.path | regex_replace('/', '_') | regex_replace('-', '_') }}_content": |
              {% for key, value in vars[item.path | regex_replace('/', '_') | regex_replace('-', '_') + '_json'].items() %}
              {{ key }}={{ value }}
              {% endfor %}
          loop: "{{ existing_app_paths }}"
          loop_control:
            label: "{{ item.path }}"

        - name: "Update .env files for each app path"
          copy:
            dest: "{{ item.path }}/.env"
            content: "{{ vars[item.path | regex_replace('/', '_') | regex_replace('-', '_') + '_content'] }}"
            owner: www-data
            group: www-data
            mode: '0644'
            force: yes
          loop: "{{ existing_app_paths }}"
          loop_control:
            label: "{{ item.path }}"

    - name: Always restart PHP-FPM
      ansible.builtin.service:
        name: php8.1-fpm
        state: restarted
      become: true