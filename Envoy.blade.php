@setup

use Aws\Ec2\Ec2Client;

function get_servers_by_aws_name($all_servers, &$deploy_servers, $aws_name, $base_name, $username) {
  $servers = [];
  $cnt = 0;
  foreach ($all_servers[$aws_name] as $ip) {
    $cnt += 1;
    $name = $base_name . $cnt;
    $deploy_servers[$name] = $username . '@' . $ip . ' -o StrictHostKeyChecking=no';
    $servers[] = $name;
  }

  return $servers;
}

function get_servers_by_pattern($all_servers, &$deploy_servers, $pattern, $base_name, $username) {
  $servers = [];
  $cnt = 0;
  foreach ($all_servers as $name => $ips) {
    if (!preg_match($pattern, $name)) {
      continue;
    }

    foreach ($ips as $ip) {
      $cnt += 1;
      $name = $base_name . $cnt;
      $deploy_servers[$name] = $username . '@' . $ip . ' -o StrictHostKeyChecking=no';
      $servers[] = $name;
    }
  }

  return $servers;
}

$all_servers = ['webserver-prod' => []];
$regions = ['us-east-1', 'us-east-2', 'us-west-1', 'ca-central-1'];

foreach ($regions as $region) {
  $ec2Client = new Aws\Ec2\Ec2Client([
    'region' => $region
  ]);

  $result = $ec2Client->describeInstances();
  foreach ($result['Reservations'] as $reservation) {
    foreach ($reservation['Instances'] as $instance) {
      if (!isset($instance['PublicIpAddress']) || !isset($instance['Tags'])) {
        continue;
      }

      $ip = $instance['PublicIpAddress'];
      $name = null;
      foreach ($instance['Tags'] as $tag) {
        if ($tag['Key'] == 'Name') {
          $name = $tag['Value'];
        }
      }

      if (isset($name) && isset($ip)) {
        if (!isset($all_servers[$name])) {
          $all_servers[$name] = [];
        }
        $all_servers[$name][] = $ip;
      }
    }
  }
}

$deploy_servers = [
  'staging' => 'ssm-user@127.0.0.1 -p 8888 -i ~/.ssh/thankview-qa-ec2.pem -o StrictHostKeyChecking=no',
  'styleguide' => 'root@*************'
];

$staging_servers = ['staging'];
$web_servers = get_servers_by_aws_name($all_servers, $deploy_servers, 'webserver-prod', 'web', 'ubuntu');
$builder_servers = get_servers_by_aws_name($all_servers, $deploy_servers, 'prod-builder-host', 'builder', 'ubuntu');
$media_servers = get_servers_by_aws_name($all_servers, $deploy_servers, 'media3.thankview.com', 'media3-', 'ubuntu');
$other_media_servers = get_servers_by_aws_name($all_servers, $deploy_servers, 'media4.thankview.com', 'media4-', 'ubuntu');
$other_media_servers = array_merge($other_media_servers, get_servers_by_aws_name($all_servers, $deploy_servers, 'media5.thankview.com', 'media5-', 'ubuntu'));
$secure_servers = get_servers_by_aws_name($all_servers, $deploy_servers, 'secure-us1.thankview.com', 'secure-us', 'ubuntu');
$secure_servers = array_merge($secure_servers, get_servers_by_aws_name($all_servers, $deploy_servers, 'secure-ca1.thankview.com', 'secure-us', 'ubuntu'));
$send_servers = get_servers_by_aws_name($all_servers, $deploy_servers, 'thankview-sends-server-static', 'send', 'ubuntu');
$video_servers = get_servers_by_aws_name($all_servers, $deploy_servers, 'video-worker-prod-static', 'video-static', 'ubuntu');
if (isset($all_servers['video-worker-prod-scaled'])) {
  $video_servers = array_merge($video_servers, get_servers_by_aws_name($all_servers, $deploy_servers, 'video-worker-prod-scaled', 'video-scaled', 'ubuntu'));
}
$sftp_servers = get_servers_by_aws_name($all_servers, $deploy_servers, 'prod-sftp-host', 'sftp', 'ubuntu');
$sftp_servers = array_merge($sftp_servers, get_servers_by_aws_name($all_servers, $deploy_servers, 'prod-ca-sftp-host', 'sftp', 'ubuntu'));
$schedule_servers = get_servers_by_aws_name($all_servers, $deploy_servers, 'prod-scheduler-host', 'schedule', 'ubuntu');
$schedule_servers = array_merge($schedule_servers, get_servers_by_aws_name($all_servers, $deploy_servers, 'prod-send-scheduler', 'send-schedule', 'ubuntu'));
$schedule_servers = array_merge($schedule_servers, get_servers_by_aws_name($all_servers, $deploy_servers, 'prod-monitor', 'monitor', 'ubuntu'));
@endsetup

@servers($deploy_servers)

@task('deploy-staging', ['on' => 'staging'])
	cd /var/www/thank-views
    sudo chown -R ssm-user:www-data .
    sudo chmod -R 644 .
    sudo chmod -R 775 .
    git stash
    git pull
	composer update
    git log -1 --pretty=%h > git_commit
	composer dump-autoload
	php artisan clear-compiled
	php artisan optimize
	php artisan config:clear
	php artisan cache:clear
	php artisan migrate
	php artisan db:seed
    php artisan testData:bootstrap
@endtask

@macro('deploy-prod', ['confirm' => true])
    deploy-prod-web
    deploy-prod-media
    {{deploy-prod-aws-workers}}
    deploy-prod-others
    deploy-prod-others-web
    deploy-prod-ca
@endmacro

@task('deploy-prod-web', ['on' => $web_servers, 'parallel' => true])
	sudo -H -u www-data /bin/bash
	export GIT_SSH_COMMAND='ssh -i /home/<USER>/thankview_deploy -o StrictHostKeyChecking=no'
	cd /var/www/thank-views
	git pull
	composer update
	git log -1 --pretty=%h > git_commit
	php artisan config:clear
	php artisan route:clear
	php artisan view:clear
    php artisan static:clear
@endtask

@task('deploy-prod-media', ['on' => $media_servers, 'parallel' => true])
	sudo -H -u www-data /bin/bash
	export GIT_SSH_COMMAND='ssh -i /home/<USER>/thankview_deploy -o StrictHostKeyChecking=no'
	cd /var/www/thank-views
	git pull
	composer update
	git log -1 --pretty=%h > git_commit
	composer dump-autoload
	php artisan config:clear
	php artisan route:clear
	php artisan view:clear
	php artisan queue:restart
@endtask

@task('deploy-prod-others', ['on' => array_merge($schedule_servers, $sftp_servers), 'parallel' => true])
	cd /var/www/thank-views
	git pull
	composer update
	git log -1 --pretty=%h > git_commit
	composer dump-autoload
@endtask

@task('deploy-prod-others-web', ['on' => array_merge($secure_servers, $other_media_servers), 'parallel' => true])
	sudo -H -u www-data /bin/bash
	export GIT_SSH_COMMAND='ssh -i /home/<USER>/thankview_deploy -o StrictHostKeyChecking=no'
	cd /var/www/thank-views
	git pull
	composer update
	git log -1 --pretty=%h > git_commit
	composer dump-autoload
@endtask

@task('deploy-prod-ca', ['on' => $schedule_servers, 'parallel' => true])
	cd /var/www/thank-views-ca
	git pull
	composer update
	git log -1 --pretty=%h > git_commit
	composer dump-autoload
@endtask

@task('deploy-prod-aws-workers', ['on' => array_merge($send_servers, $video_servers), 'parallel' => true])
    export GIT_SSH_COMMAND='ssh -i /home/<USER>/.ssh/thankview_deploy -o StrictHostKeyChecking=no'
    cd /var/www/thank-views
    git pull
	composer update
    git log -1 --pretty=%h > git_commit
    composer dump-autoload
    php artisan config:clear
    php artisan route:clear
    php artisan view:clear
    php artisan queue:restart
    php artisan file:deleteTemp
@endtask

@task('deploy-builder-prod', ['on' => $builder_servers, 'confirm' => true])
    cd /var/www/ThankView-Envelope-Builder
    git pull
	composer update
    git log -1 --pretty=%h > git_commit
    composer dump-autoload
@endtask

@task('test-prod-all', ['on' => array_merge($web_servers, $builder_servers, $media_servers, $schedule_servers, $sftp_servers, $secure_servers, $other_media_servers), 'parallel' => true])
    ls
@endtask

@after
   @slack('*******************************************************************************', '#devalerts')
@endafter
