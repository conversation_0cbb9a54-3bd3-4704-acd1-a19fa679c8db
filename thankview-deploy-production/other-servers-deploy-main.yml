# other-servers-deploy-main.yml - Equivalent of the Envoy deploy-prod-others task
- name: Deploy to other servers (schedule, sftp, monitor, other media)
  hosts: prod_others
  vars:
    app_path: /var/www/thank-views
    git_repo: "**************:evertrue/ThankView-App.git"
    git_branch: "{{ deploy_branch | default('production') }}"
    user: 'ubuntu'
  tasks:
    - name: Get SSH key using AWS CLI
      ansible.builtin.shell: |
        aws ssm get-parameter --name "/thankview-prod/thankview_deploy" --with-decryption --region us-east-1 --query Parameter.Value --output text
      register: ssh_key_cli
      delegate_to: localhost
      run_once: true
      no_log: true

    - name: Create .ssh directory
      ansible.builtin.file:
        path: "/home/<USER>/.ssh"
        state: directory
        mode: '0700'
        owner: "{{ user }}"
        group: "{{ user }}"
      become: true

    - name: Save SSH key from CLI to file
      ansible.builtin.copy:
        content: "{{ ssh_key_cli.stdout }}\n"
        dest: "/home/<USER>/.ssh/thankview_deploy"
        mode: '0600'
        owner: "{{ user }}"
        group: "{{ user }}"
      become: true
      no_log: true

    - name: Install Python pip
      apt:
        name: python3-pip
        state: present
        update_cache: yes
      become: true

    - name: Install required Python modules for AWS operations
      pip:
        name:
          - boto3
          - botocore
        state: present
        executable: pip3
        extra_args: "--system"  # Install system-wide
      become: true

    - name: Check if git repo already exists
      ansible.builtin.stat:
        path: "{{ app_path }}/.git"
      register: git_dir

    - name: Clone git repository if it doesn't exist
      block:
        - name: Ensure app directory exists
          ansible.builtin.file:
            path: "{{ app_path }}"
            state: directory
            mode: '0755'
            owner: "{{ user }}"
            group: "{{ user }}"
          become: true

        - name: Clone repository
          ansible.builtin.git:
            repo: "{{ git_repo }}"
            dest: "{{ app_path }}"
            key_file: "/home/<USER>/.ssh/thankview_deploy"
            accept_hostkey: yes
            ssh_opts: "-o StrictHostKeyChecking=no"
          become: true
          become_user: "{{ user }}"
      when: not git_dir.stat.exists

    # This will reset repo and drop any local changes before pulling latest repo commit
    - name: Reset repository to the latest commit
      ansible.builtin.shell:
        cmd: |
          git reset --hard HEAD
          git clean -fd
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: "{{ user }}"
      when: git_dir.stat.exists

    - name: Allow Git to work in this repository
      ansible.builtin.shell:
        cmd: git config --global --add safe.directory /var/www/thank-views
      become: true
      become_user: "{{ user }}"

    - name: Pull latest code if repo exists
      ansible.builtin.git:
        repo: "{{ git_repo }}"
        dest: "{{ app_path }}"
        update: yes
        version: "{{ git_branch }}"
        key_file: "/home/<USER>/.ssh/thankview_deploy"
        accept_hostkey: yes
        ssh_opts: "-o StrictHostKeyChecking=no"
        force: yes 
      when: git_dir.stat.exists
      become: true
      become_user: "{{ user }}"

    - name: Run composer update
      community.general.composer:
        command: update
        working_dir: "{{ app_path }}"
      environment:
        COMPOSER_DISCARD_CHANGES: "true"
      become: true
      become_user: "{{ user }}"
      register: composer_result
      failed_when: 
        - composer_result.failed == true
        - "'ddtrace.so' not in composer_result.msg"
        - "'Script php artisan package:discover handling the post-autoload-dump event returned with error code 1' not in composer_result.msg"

    - name: Save git commit hash
      ansible.builtin.shell: git log -1 --pretty=%h > git_commit
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: "{{ user }}"

    - name: Run composer dump-autoload
      community.general.composer:
        command: dump-autoload
        working_dir: "{{ app_path }}"
      become: true
      become_user: "{{ user }}"
      register: composer_result
      failed_when: 
        - composer_result.failed == true
        - "'ddtrace.so' not in composer_result.msg"
        - "'Script php artisan package:discover handling the post-autoload-dump event returned with error code 1' not in composer_result.msg"

    # New task to fetch and update .env files from AWS Secrets Manager
    - name: Overwrite .env files from Secrets Manager via lookup
      become: true
      block:
        - name: Gather EC2 metadata facts
          ec2_metadata_facts:
          register: ec2_metadata

        - name: Get instance ID from metadata
          set_fact:
            instance_id: "{{ ec2_metadata.ansible_facts.ansible_ec2_instance_id }}"

        - name: Get EC2 instance info to determine server name
          ec2_instance_info:
            instance_ids: "{{ instance_id }}"
            region: "{{ ec2_metadata.ansible_facts.ansible_ec2_placement_region }}"
          register: server_info
          delegate_to: localhost
          become: false

        - name: Extract server name from EC2 Name tag
          set_fact:
            server_name: "{{ item.value }}"
          loop: "{{ server_info.instances[0].tags | dict2items }}"
          when: item.key == 'Name'

        - name: Extract base server name (everything after 'prod-')
          set_fact:
            base_server_name: "{{ server_name | regex_replace('^prod-(.*)$', '\\1') }}"

        - name: Debug server names
          debug:
            msg: "Full server name: {{ server_name }}, Base name: {{ base_server_name }}"

        - name: Define potential app paths to check
          set_fact:
            potential_app_paths:
              - path: /var/www/thank-views
                secret_suffix: "-env"
              - path: /var/www/thank-views-ca
                secret_suffix: "-ca-env"

        - name: Check which app paths exist on this server
          stat:
            path: "{{ item.path }}"
          loop: "{{ potential_app_paths }}"
          register: app_path_stats
          loop_control:
            label: "{{ item.path }}"

        - name: Build list of existing app paths with their secrets
          set_fact:
            existing_app_paths: "{{ existing_app_paths | default([]) + [{'path': item.item.path, 'secret_name': 'prod/' + base_server_name + item.item.secret_suffix}] }}"
          loop: "{{ app_path_stats.results }}"
          when: item.stat.exists and item.stat.isdir
          loop_control:
            label: "{{ item.item.path }}"

        - name: Debug existing app paths and their secrets
          debug:
            msg: "Server {{ server_name }} has app path {{ item.path }} using secret {{ item.secret_name }}"
          loop: "{{ existing_app_paths }}"

        - name: "Fetch secrets for each app path"
          set_fact:
            "{{ item.path | regex_replace('/', '_') | regex_replace('-', '_') }}_json": "{{ lookup('aws_secret', item.secret_name, region='us-east-1') }}"
          loop: "{{ existing_app_paths }}"
          loop_control:
            label: "{{ item.secret_name }}"

        - name: "Convert JSON to .env format for each app path"
          set_fact:
            "{{ item.path | regex_replace('/', '_') | regex_replace('-', '_') }}_content": |
              {% for key, value in vars[item.path | regex_replace('/', '_') | regex_replace('-', '_') + '_json'].items() %}
              {{ key }}={{ value }}
              {% endfor %}
          loop: "{{ existing_app_paths }}"
          loop_control:
            label: "{{ item.path }}"

        - name: "Update .env files for each app path"
          copy:
            dest: "{{ item.path }}/.env"
            content: "{{ vars[item.path | regex_replace('/', '_') | regex_replace('-', '_') + '_content'] }}"
            owner: www-data
            group: www-data
            mode: '0644'
            force: yes
          loop: "{{ existing_app_paths }}"
          loop_control:
            label: "{{ item.path }}"

    # Task to set APP_ROLE based on instance tags
    - name: Set APP_ROLE based on server type
      become: true
      block:
        - name: Check if APP_ROLE already exists in .env file
          shell: grep -c "^APP_ROLE=" /var/www/thank-views/.env || true
          register: app_role_exists
          changed_when: false

        - name: Gather EC2 facts to get instance tags
          ec2_metadata_facts:
          register: ec2_facts

        - name: Get instance ID
          set_fact:
            instance_id: "{{ ec2_facts.ansible_facts.ansible_ec2_instance_id }}"
          when: ec2_facts is defined

        - name: Fix MySQL repository GPG key
          apt_key:
            keyserver: keyserver.ubuntu.com
            id: B7B3B788A8D3785C
            state: present
          become: yes

        - name: Update apt cache with release info changes
          shell: apt update --allow-releaseinfo-change
          environment:
            DEBIAN_FRONTEND: noninteractive
          become: yes  

        - name: Describe EC2 instance to get tags
          ec2_instance_info:
            instance_ids: "{{ instance_id }}"
            region: "{{ ec2_facts.ansible_facts.ansible_ec2_placement_region }}"
          register: instance_info
          vars:
            ansible_python_interpreter: /usr/bin/python3  # Explicitly set Python interpreter
          when: instance_id is defined

        - name: Extract APP_ROLE from tags
          set_fact:
            app_role_value: "{{ item.value }}"
          loop: "{{ instance_info.instances[0].tags | dict2items }}"  # Fixed loop using dict2items
          when:
            - instance_info is defined
            - instance_info.instances is defined
            - instance_info.instances | length > 0
            - item.key == 'APP_ROLE'

        - name: Debug APP_ROLE value from tags
          debug:
            msg: "Found APP_ROLE tag with value: {{ app_role_value }}"
          when: app_role_value is defined and app_role_value != ''

        - name: Set APP_ROLE in all .env files from instance tag
          lineinfile:
            path: "{{ item.path }}/.env"
            regexp: '^APP_ROLE='
            line: "APP_ROLE={{ app_role_value }}"
            state: present
          loop: "{{ existing_app_paths }}"
          when:
            - app_role_exists.stdout == "0" or app_role_exists.stdout == "1"
            - app_role_value is defined
            - app_role_value != ''
          loop_control:
            label: "{{ item.path }}"

    - name: Always restart PHP-FPM
      ansible.builtin.service:
        name: php8.1-fpm
        state: restarted
      become: true